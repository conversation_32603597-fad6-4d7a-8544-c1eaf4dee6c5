sources:
  my-mysql-source:
    kind: mysql
    host: 127.0.0.1
    port: 3306
    database: liquor_store
    user: root
    password: root

tools:
  get_beers:
    kind: mysql-sql
    source: my-mysql-source
    statement: |
      SELECT * FROM beers
      WHERE style = ?
      AND availability = ?
      LIMIT 10
    description: |
      Get beers from the liquor store database that match the specified criteria.
      The criteria are:
      - style: English Oatmeal Stout
      - availability: Limited (brewed once)
      - limit: 10
    parameters:
      - name: style
        type: string
        description: The style of the beer
      - name: availability
        type: string
        description: The availability of the beer

toolsets:
  my_instance_toolset:
    - get_beers
